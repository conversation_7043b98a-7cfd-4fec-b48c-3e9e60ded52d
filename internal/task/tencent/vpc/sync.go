package vpc

import (
	"log/slog"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent"
)

type Syncer struct {
	cred      *tencent.FactoryCredential
	log       *slog.Logger
	factoryID int
	accountID int
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := tencent.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.Name)

	s := &Syncer{
		cred:      cred,
		log:       log,
		factoryID: cred.Factory.GetID(),
		accountID: cred.Account.GetID(),
	}
	return s, nil
}

// FactoryField 返回工厂字段
func (s *Syncer) FactoryField() fields.Field {
	return fields.FactoryField(s.factoryID)
}

// FactoryAccountField 返回账户字段
func (s *Syncer) FactoryAccountField() fields.Field {
	return fields.FactoryAccountField(s.accountID)
}

// GetFactoryID 返回工厂ID
func (s *Syncer) GetFactoryID() int {
	return s.factoryID
}

// GetAccountID 返回账户ID
func (s *Syncer) GetAccountID() int {
	return s.accountID
}

// FactoryFields 返回工厂和账户字段的便利方法
func (s *Syncer) FactoryFields() []fields.Field {
	return []fields.Field{
		s.FactoryField(),
		s.FactoryAccountField(),
	}
}

// Sync 同步所有资源, 同步完成后执行清理
// 同步的资源: VPC, EIP, SecurityGroup, SecurityGroupRule
func (s *Syncer) Sync() error {
	// 记录同步开始时间，用于后续清理
	syncStartTime := time.Now().Unix()

	regions, err := restclient.ListAll[models.Region](fields.NamedField("factory__id", s.factoryID))
	if err != nil {
		return err
	}

	var lastError error
	for _, region := range regions.Results {
		if err := s.SyncInRegion(&region); err != nil {
			lastError = err
		}
	}

	// 如果同步成功，执行清理操作
	if lastError == nil {
		if cleanErr := s.Clean(syncStartTime); cleanErr != nil {
			s.log.Warn("Sync completed but cleanup failed", "error", cleanErr)
			lastError = cleanErr
		}
	} else {
		s.log.Warn("Skipping cleanup due to sync errors", "error", lastError)
	}

	return lastError
}

const pageSize int64 = 100

// syncTask 定义同步任务的结构
type syncTask struct {
	name string
	fn   func(*vpc.Client, *models.Region) error
}

// getSyncTasks 返回所有需要执行的同步任务
func (s *Syncer) getSyncTasks() []syncTask {
	return []syncTask{
		{"VPC", s.SyncVPC},
		{"EIP", s.SyncEIP},
		{"SecurityGroup", s.SyncSG},
		// 新增资源同步时，只需要在这里添加一行即可
	}
}

// executeSyncTasks 执行所有同步任务，返回失败的资源列表和最后一个错误
func (s *Syncer) executeSyncTasks(client *vpc.Client, region *models.Region, tasks []syncTask) ([]string, error) {
	var lastError error
	var failedResources []string

	for _, task := range tasks {
		if err := task.fn(client, region); err != nil {
			lastError = err
			failedResources = append(failedResources, task.name)
			s.log.Error("Resource sync failed, continuing with other resources",
				"resource", task.name, "region", region.RegionID, "error", err)
		} else {
			s.log.Debug("Resource sync completed successfully",
				"resource", task.name, "region", region.RegionID)
		}
	}

	return failedResources, lastError
}

func (s *Syncer) SyncInRegion(region *models.Region) error {
	client, err := vpc.NewClient(s.cred.Credential, region.RegionID, profile.NewClientProfile())
	if err != nil {
		return err
	}

	// 获取所有同步任务并执行
	tasks := s.getSyncTasks()
	failedResources, lastError := s.executeSyncTasks(client, region, tasks)

	// 记录同步总结
	if len(failedResources) > 0 {
		s.log.Warn("Region sync completed with failures", "region", region.RegionID, "failed_resources", failedResources)
	} else {
		s.log.Info("Region sync completed successfully", "region", region.RegionID)
	}

	return lastError
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	if err := s.Sync(); err != nil {
		return err
	}

	return nil
}
