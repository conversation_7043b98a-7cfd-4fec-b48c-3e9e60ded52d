package vpc

import (
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/common"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncEIP(client *vpc.Client, region *models.Region) error {
	request := vpc.NewDescribeAddressesRequest()
	request.Limit = fields.Int64(pageSize)

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Int64(page * pageSize)

		response, err := client.DescribeAddresses(request)
		if err != nil {
			s.log.Error("Failed to describe eips", "region", region.RegionID, "error", err)
			return err
		}

		for _, addr := range response.Response.AddressSet {
			conds := []fields.Field{
				s.FactoryField(),
				fields.ExternalUUIDField(*addr.AddressId),
			}

			// 使用 BeautyWithSyncer 统一处理工厂、账户和区域字段
			attrs := common.BeautyWithSyncer(addr, BeautyAddress, s, region.GetID())

			if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attrs); perr != nil {
				lastError = perr
				s.log.Error("Failed to sync eip", "id", *addr.AddressId, "region", region.RegionID, "error", perr)
			} else {
				s.log.Debug("Synced eip", "id", *addr.AddressId, "region", region.RegionID, "attr", attrs)
			}
		}

		if len(response.Response.AddressSet) < int(pageSize) {
			break
		}
	}

	return lastError
}
