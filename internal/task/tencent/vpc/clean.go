package vpc

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// Clean 清理指定时间戳之前的过期数据
func (s *Syncer) Clean(timestamp int64) error {
	var lastError error

	// 清理过期的 VPC
	if err := s.cleanVPCs(timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean VPCs", "error", err)
	} else {
		s.log.Info("VPC cleanup completed successfully")
	}

	// 清理过期的 EIP
	if err := s.cleanEIPs(timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean EIPs", "error", err)
	} else {
		s.log.Info("EIP cleanup completed successfully")
	}

	// 清理过期的安全组
	if err := s.cleanSecurityGroups(timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean security groups", "error", err)
	} else {
		s.log.Info("Security group cleanup completed successfully")
	}

	if lastError == nil {
		s.log.Info("All VPC resources cleanup completed successfully", "timestamp", timestamp)
	} else {
		s.log.Warn("VPC resources cleanup completed with some failures", "timestamp", timestamp)
	}

	return lastError
}

// cleanVPCs 清理过期的 VPC 资源
func (s *Syncer) cleanVPCs(timestamp int64) error {
	conds := []fields.Field{
		s.FactoryField(),
		s.FactoryAccountField(),
		fields.LessThan("update_at", timestamp),
	}

	resp, err := restclient.ListAll[models.VPC](conds...)
	if err != nil {
		s.log.Error("Failed to list VPCs for cleanup", "error", err)
		return err
	}

	var deletedCount int
	var lastError error

	for _, vpc := range resp.Results {
		if err := restclient.Delete(vpc); err != nil {
			lastError = err
			s.log.Error("Failed to delete VPC", "id", vpc.GetID(), "error", err)
		} else {
			deletedCount++
			s.log.Debug("Deleted VPC", "id", vpc.GetID())
		}
	}

	s.log.Info("Deleted expired VPCs", "count", deletedCount, "total", resp.Count, "timestamp", timestamp)
	return lastError
}

// cleanEIPs 清理过期的 EIP 资源
func (s *Syncer) cleanEIPs(timestamp int64) error {
	conds := []fields.Field{
		s.FactoryField(),
		s.FactoryAccountField(),
		fields.LessThan("update_at", timestamp),
	}

	resp, err := restclient.ListAll[models.ElasticIP](conds...)
	if err != nil {
		s.log.Error("Failed to list EIPs for cleanup", "error", err)
		return err
	}

	var deletedCount int
	var lastError error

	for _, eip := range resp.Results {
		if err := restclient.Delete(eip); err != nil {
			lastError = err
			s.log.Error("Failed to delete EIP", "id", eip.GetID(), "error", err)
		} else {
			deletedCount++
			s.log.Debug("Deleted EIP", "id", eip.GetID())
		}
	}

	s.log.Info("Deleted expired EIPs", "count", deletedCount, "total", resp.Count, "timestamp", timestamp)
	return lastError
}

// cleanSecurityGroups 清理过期的安全组资源
func (s *Syncer) cleanSecurityGroups(timestamp int64) error {
	conds := []fields.Field{
		s.FactoryField(),
		s.FactoryAccountField(),
		fields.LessThan("update_at", timestamp),
	}

	resp, err := restclient.ListAll[models.Security](conds...)
	if err != nil {
		s.log.Error("Failed to list security groups for cleanup", "error", err)
		return err
	}

	var deletedCount int
	var lastError error

	for _, sg := range resp.Results {
		if err := restclient.Delete(sg); err != nil {
			lastError = err
			s.log.Error("Failed to delete security group", "id", sg.GetID(), "error", err)
		} else {
			deletedCount++
			s.log.Debug("Deleted security group", "id", sg.GetID())
		}
	}

	s.log.Info("Deleted expired security groups", "count", deletedCount, "total", resp.Count, "timestamp", timestamp)
	return lastError
}
