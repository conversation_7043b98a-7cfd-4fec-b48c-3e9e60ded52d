package vpc

import (
	"fmt"
	"time"

	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/common"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// SyncSG syncs security groups in the specified region
func (s *Syncer) SyncSG(client *vpc.Client, region *models.Region) error {
	request := vpc.NewDescribeSecurityGroupsRequest()
	request.Limit = fields.Pointer("100")

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Pointer(fmt.Sprintf("%d", page*pageSize))

		response, err := client.DescribeSecurityGroups(request)
		if err != nil {
			s.log.Error("Failed to describe security groups", "region", region.RegionID, "error", err)
			return err
		}

		for _, sg := range response.Response.SecurityGroupSet {
			// 使用 BeautyWithSyncer 统一处理工厂、账户和区域字段
			attrs := common.BeautyWithSyncer(sg, BeautySecurityGroup, s, region.GetID())

			conds := []fields.Field{
				s.FactoryField(),
				*attrs.GetField("security_id"),
			}

			if securityGroup, perr := restclient.PostOrPatch[models.Security](conds, attrs); perr != nil {
				lastError = perr
				s.log.Error("Failed to sync security group", "security_id", *sg.SecurityGroupId, "id", securityGroup.GetID(), "region", region.RegionID, "error", perr)
			} else {
				s.log.Debug("Synced security group", "security_id", *sg.SecurityGroupId, "id", securityGroup.GetID(), "region", region.RegionID, "attr", attrs)

				// 记录规则同步开始时间，用于清理过期规则
				ruleSyncStart := time.Now().Unix()

				// 同步安全组规则
				if ruleErr := s.syncSecurityGroupRules(client, *sg.SecurityGroupId, securityGroup.GetID()); ruleErr != nil {
					lastError = ruleErr
					s.log.Error("Failed to sync security group rules", "sg_id", *sg.SecurityGroupId, "error", ruleErr)
				} else {
					// 清理过期的规则（在同步开始前就存在的规则）
					restclient.DeleteSubResource[models.Security]("rules", securityGroup.SecurityID,
						fields.NumberField("updated_before", ruleSyncStart))
					s.log.Debug("Cleaned up outdated security group rules", "sg_id", *sg.SecurityGroupId)
				}
			}
		}

		if len(response.Response.SecurityGroupSet) < int(pageSize) {
			break
		}
	}

	return lastError
}

// syncSecurityGroupRules 同步安全组规则
func (s *Syncer) syncSecurityGroupRules(client *vpc.Client, sgId string, securityGroupID int) error {
	// 获取安全组规则
	request := vpc.NewDescribeSecurityGroupPoliciesRequest()
	request.SecurityGroupId = &sgId

	response, err := client.DescribeSecurityGroupPolicies(request)
	if err != nil {
		return err
	}

	var lastError error

	// 同步入站规则
	if response.Response.SecurityGroupPolicySet.Ingress != nil {
		for _, rule := range response.Response.SecurityGroupPolicySet.Ingress {
			if err := s.syncSecurityGroupRule(rule, securityGroupID, "ingress"); err != nil {
				lastError = err
			}
		}
	}

	// 同步出站规则
	if response.Response.SecurityGroupPolicySet.Egress != nil {
		for _, rule := range response.Response.SecurityGroupPolicySet.Egress {
			if err := s.syncSecurityGroupRule(rule, securityGroupID, "egress"); err != nil {
				lastError = err
			}
		}
	}

	return lastError
}

// syncSecurityGroupRule 同步单个安全组规则
func (s *Syncer) syncSecurityGroupRule(rule *vpc.SecurityGroupPolicy, securityGroupID int, direction string) error {
	// 构建规则的唯一标识
	ruleKey := fmt.Sprintf("%s_%s_%s_%s_%s",
		direction,
		fields.Value(rule.Protocol),
		fields.Value(rule.Port),
		fields.Value(rule.CidrBlock),
		fields.Value(rule.SecurityGroupId))

	conds := []fields.Field{
		fields.NamedField("security", securityGroupID),
		fields.StringField("rule_id", ruleKey),
	}

	attrs := BeautyWith(rule,
		func(r *vpc.SecurityGroupPolicy) fields.Fields {
			return BeautySecurityGroupRule(r, direction, ruleKey)
		},
		fields.NamedField("security", securityGroupID),
	)

	_, err := restclient.PostOrPatch[models.Rule](conds, attrs)
	if err != nil {
		s.log.Error("Failed to sync security group rule", "rule_key", ruleKey, "error", err)
	} else {
		s.log.Debug("Synced security group rule", "rule_key", ruleKey)
	}

	return err
}
