package vpc

import (
	"fmt"

	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncVPC(client *vpc.Client, region *models.Region) error {
	request := vpc.NewDescribeVpcsRequest()
	request.Limit = fields.Pointer(fmt.Sprintf("%d", pageSize))

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Pointer(fmt.Sprintf("%d", page*pageSize))

		response, err := client.DescribeVpcs(request)
		if err != nil {
			s.log.Error("Failed to describe vpcs", "region", region.RegionID, "error", err)
			return err
		}

		for _, vpc := range response.Response.VpcSet {
			conds := []fields.Field{
				fields.FactoryField(s.factoryID),
				fields.ExternalUUIDField(*vpc.VpcId),
			}

			factoryFields := s.FactoryFields()
			attrs := BeautyWith(vpc,
				BeautyVPC,
				factoryFields[0], // FactoryField
				factoryFields[1], // FactoryAccountField
				fields.RegionField(region.GetID()),
			)

			if _, perr := restclient.PostOrPatch[models.VPC](conds, attrs); perr != nil {
				lastError = perr
				s.log.Error("Failed to sync vpc", "id", *vpc.VpcId, "region", region.RegionID, "error", perr)
			} else {
				s.log.Debug("Synced vpc", "id", *vpc.VpcId, "region", region.RegionID, "attr", attrs)
			}
		}

		// 不足一页，说明已经全部同步完毕
		if len(response.Response.VpcSet) < int(pageSize) {
			break
		}
	}

	return lastError
}
