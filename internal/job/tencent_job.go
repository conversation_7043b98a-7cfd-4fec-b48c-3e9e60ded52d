package job

import (
	"sync"

	qecs "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent/ecs"
	qtke "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent/tke"
	qvpc "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent/vpc"
)

func TencentSync(accountKey string) error {
	wg := sync.WaitGroup{}

	// CVM
	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		qecs.Sync(account)
	}(accountKey)

	// TKE
	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		qtke.Sync(account)
	}(accountKey)

	// VPC(vpc, eip, security group and rules)
	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		qvpc.Sync(account)
	}(accountKey)

	// wait for all tasks to finish
	wg.Wait()

	return nil
}
