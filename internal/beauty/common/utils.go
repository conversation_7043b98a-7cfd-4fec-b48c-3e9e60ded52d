package common

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// BeautyOptions 通用 Beauty 配置选项
type BeautyOptions struct {
	FactoryID  int
	AccountID  int
	RegionID   int
	ZoneID     *int
	ProductKey string // 用于产品分类的关键字段
}

// WithFactoryFields 添加工厂和账户字段
func WithFactoryFields(attrs fields.Fields, opts BeautyOptions) fields.Fields {
	return attrs.With(
		fields.FactoryField(opts.FactoryID),
		fields.FactoryAccountField(opts.AccountID),
	)
}

// WithLocationFields 添加位置相关字段
func WithLocationFields(attrs fields.Fields, opts BeautyOptions) fields.Fields {
	attrs = attrs.With(fields.RegionField(opts.RegionID))
	if opts.ZoneID != nil {
		attrs = attrs.With(fields.ZoneField(*opts.ZoneID))
	}
	return attrs
}

// WithProductField 添加产品字段（基于名称自动分类）
func WithProductField(attrs fields.Fields, name string) fields.Fields {
	if name != "" {
		if productID := category.Category(name, ""); productID != nil {
			attrs = attrs.With(fields.ProductField(*productID))
		}
	}
	return attrs
}

// StandardizeStatus 标准化状态字段
func StandardizeStatus(status string) string {
	switch status {
	case "creating", "pending":
		return "creating"
	case "running", "active", "available", "normal":
		return "running"
	case "stopped", "inactive":
		return "stopped"
	case "deleting", "terminating":
		return "deleting"
	default:
		return status
	}
}

// ParseTimeToRFC3339 解析时间并转换为 RFC3339 格式
func ParseTimeToRFC3339(timeStr string, layout string) string {
	if timeStr == "" {
		return ""
	}

	if t, err := time.ParseInLocation(layout, timeStr, time.Local); err == nil {
		return t.Format(time.RFC3339)
	}
	return timeStr
}

// BeautyWithCommon 通用的 Beauty 增强函数
func BeautyWithCommon[T any](
	obj T,
	beautyFunc func(T) fields.Fields,
	opts BeautyOptions,
	additions ...fields.Field,
) fields.Fields {
	attrs := beautyFunc(obj)

	// 添加工厂和账户字段
	attrs = WithFactoryFields(attrs, opts)

	// 添加位置字段
	attrs = WithLocationFields(attrs, opts)

	// 添加产品字段（如果指定了产品关键字）
	if opts.ProductKey != "" {
		attrs = WithProductField(attrs, opts.ProductKey)
	}

	// 添加额外字段
	if len(additions) > 0 {
		attrs = attrs.With(additions...)
	}

	return attrs
}
