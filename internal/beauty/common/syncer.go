package common

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// SyncerInterface 定义同步器的通用接口
type SyncerInterface interface {
	FactoryField() fields.Field
	FactoryAccountField() fields.Field
	GetFactoryID() int
	GetAccountID() int
}

// BeautyOptions 从 Syncer 创建配置选项
func NewBeautyOptionsFromSyncer(s SyncerInterface, regionID int) BeautyOptions {
	return BeautyOptions{
		FactoryID: s.GetFactoryID(),
		AccountID: s.GetAccountID(),
		RegionID:  regionID,
	}
}

// WithSyncerFields 使用 Syncer 添加工厂和账户字段
func WithSyncerFields(attrs fields.Fields, s SyncerInterface) fields.Fields {
	return attrs.With(
		s.FactoryField(),
		s.FactoryAccountField(),
	)
}

// BeautyWithSyncer 使用 Syncer 的通用 Beauty 增强函数
func BeautyWithSyncer[T any](
	obj T,
	beautyFunc func(T) fields.Fields,
	s SyncerInterface,
	regionID int,
	additions ...fields.Field,
) fields.Fields {
	attrs := beautyFunc(obj)

	// 添加工厂和账户字段
	attrs = WithSyncerFields(attrs, s)

	// 添加区域字段
	attrs = attrs.With(fields.RegionField(regionID))

	// 添加额外字段
	if len(additions) > 0 {
		attrs = attrs.With(additions...)
	}

	return attrs
}
